{"name": "first-spark-app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "eslint . --ext .js,.ts,.svelte --fix", "lint:format": "prettier --check . && eslint .", "test": "vitest", "test:unit": "vitest --project unit", "test:unit:ci": "vitest run --project unit", "test:storybook": "vitest --project storybook", "test:storybook:ci": "vitest run --project storybook", "test:e2e": "playwright test --headed", "test:e2e:ci": "playwright test", "test:all": "pnpm run test:unit:ci && pnpm run test:e2e:ci", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@inlang/paraglide-js": "2.0.8", "@lucide/svelte": "^0.482.0", "@playwright/test": "^1.52.0", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-svelte-csf": "^5.0.0-next.0", "@storybook/blocks": "^8.6.7", "@storybook/experimental-addon-test": "^8.6.7", "@storybook/manager-api": "^8.6.9", "@storybook/svelte": "^8.6.7", "@storybook/sveltekit": "^8.6.7", "@storybook/test": "^8.6.7", "@storybook/theming": "^8.6.9", "@svelte-put/cloudflare-turnstile": "^1.2.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.7", "@types/node": "^22.13.5", "@types/throttle-debounce": "^5.0.2", "@vitest/browser": "3.1.3", "@vitest/coverage-v8": "3.1.3", "autoprefixer": "^10.4.20", "bits-ui": "^1.8.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-svelte": "^2.46.1", "globals": "^15.14.0", "jsdom": "^26.1.0", "lucide-svelte": "^0.475.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "storybook": "^8.6.7", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^0.3.28", "tailwind-merge": "^3.0.1", "tailwind-variants": "^0.3.1", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vitest": "^3.1.3"}, "dependencies": {"@baragaun/bg-node-client": "^0.0.53", "@sparticuz/chromium": "^137.0.1", "bwip-js": "^4.6.0", "formsnap": "^2.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mode-watcher": "^0.5.1", "puppeteer-core": "^24.10.2", "quagga": "^0.12.1", "rxdb": "^16.8.1", "sveltekit-superforms": "^2.23.1", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.1", "throttle-debounce": "^5.0.2", "zod": "^3.24.2"}, "pnpm": {"onlyBuiltDependencies": ["@firebase/util", "esbuild", "msw", "protobufjs", "rxdb", "svelte-preprocess"]}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}